import { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Image, ListGroup } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import 'animate.css';

gsap.registerPlugin(ScrollTrigger);

const contractStaffingData = {
    hero: {
        title: "Contract Staffing Services",
        subtitle: "On Target, Temporary Staffing Small or Large-Scale. Organizations are largely dependent on manpower requirements. We'll cater to your multiple requirements and design customizable, scalable solutions as per need.",
        backgroundImage: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1926&q=80'
    },
    sections: [
        {
            id: 'benefits',
            title: "Benefits of Contract Staffing Services",
            texts: [
                ""
            ],
            listItems: [
                "Flexibility: Gain access to people with specialized skills through temporary staffing without hiring them permanently.",
                "Cost-effectiveness: Reduce overhead costs by only paying for labor needed during a project, eliminating the need for benefits and long-term employment costs.",
                "Reduced Administrative Burden: Payroll, taxes and compliance are all handled by the staffing agency, reducing the administrative burden on HR.",
                "Risk Mitigation: You don't have to hire people who may not be suitable for the long run, as it is temporary staffing.",
                "No Loss of Productivity: Bring in additional employees and ensure an increase in productivity without losing existing employee engagement.",
                // "**Faster Hiring:** Have access to a wider talent pool and a more robust talent pipeline to secure the best talent for the job in very little time.",
                // "**Scalability:** Easily scale up or down the workforce depending on project or the business' unique requirements at a given time."
            ],
            image: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Benefits of contract staffing services',
            reversed: false
        },
        {
            id: 'approach',
            title: "Our Approach",
            texts: [
                ""
            ],
            listItems: [
                "Identifying and Shortlisting: Our dedicated, expert team understands your workforce and manpower needs, from top level to junior-level candidate requirements.",
                "Customized, Scalable: We design a customizable model and form a strategy to meet your requirements with access to pre-validated candidates.",
                "Quality Candidates Access: We have access to a huge talent pool, sourced from various talent pipelines like online portals, recruitment drives, and social media.",
                "Onboarding Process: We put in extra effort into our temporary staffing, helping your new hire settle in once they are onboard.",
                // "**Payroll Process and Compliance:** Our payroll team processes timely payroll and maintains statutory compliance for all our partners.",
                // "**Handling Client Queries:** A dedicated relationship manager and team are at your disposal to handle any queries or questions you may have."
            ],
            image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Contract staffing approach and methodology',
            reversed: true
        },
        {
            id: 'why-work-with-us',
            title: "Why Work With Us?",
            texts: [
                ""
            ],
            listItems: [
                "Easy to Get Started: Simple processes and clear SLAs make it easy to get started regardless of team size.",
                "Build Your Team Quickly: Thanks to our established processes, you can build a team quickly using our contract staffing solution.",
                "Single Point of Contact: Our dedicated relationship manager handles onboarding, attendance management, payroll etc.",
                "Large Database: Our access to a large database with variety of specializations for FMCG, FMCD, Telecom, and Finance.",
                "Automated Payroll: We have an efficient payroll program that is secure and error-free, paying employees on designated dates.",
                "Technology-driven: Advanced computerized system with self-service portals, MIS and query management system.",
                // "**Complete Compliance Management:** We provide complete compliance management for benefits compliance, PF, ESIC and labour laws.",
                // "**Proven History:** We have a legacy in HR consulting and are proud of what we have achieved."
            ],
            image: 'https://images.unsplash.com/photo-1521737711867-e3b97375f902?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
            alt: 'Why choose our contract staffing services',
            reversed: false
        }
    ]
};

const ContractStaffingPage = () => {
    const heroRef = useRef(null);
    const sectionsRef = useRef([]);

    useEffect(() => {
        window.scrollTo(0, 0);

        // GSAP Animations
        const ctx = gsap.context(() => {
            // Hero section animation
            gsap.from(heroRef.current.querySelector('h1'), {
                y: 100,
                opacity: 0,
                duration: 1.2,
                ease: "power3.out"
            });

            gsap.from(heroRef.current.querySelector('p'), {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: "power2.out",
                delay: 0.3
            });

            gsap.from(heroRef.current.querySelector('.btn'), {
                y: 30,
                opacity: 0,
                duration: 0.8,
                ease: "back.out(1.7)",
                delay: 0.6
            });

            // Sections animations
            sectionsRef.current.forEach((section) => {
                if (section) {
                    gsap.from(section.querySelector('h2'), {
                        y: 50,
                        opacity: 0,
                        duration: 1,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 80%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });

                    gsap.from(section.querySelectorAll('p'), {
                        y: 30,
                        opacity: 0,
                        duration: 0.8,
                        stagger: 0.2,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 75%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });

                    gsap.from(section.querySelector('img'), {
                        scale: 0.8,
                        opacity: 0,
                        duration: 1,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: section,
                            start: "top 70%",
                            end: "bottom 20%",
                            toggleActions: "play none none reverse"
                        }
                    });
                }
            });



        }, heroRef);

        return () => ctx.revert();
    }, []);

    const [hoveredImage, setHoveredImage] = useState(null);

    // Reusable Style Objects
    const primaryColor = '#007bff';
    const primaryDarkColor = '#0056b3';
    const primaryRgb = '0,123,255';

    const ctaButtonBaseStyle = {
        padding: '1.2rem 3rem',
        fontSize: '1.2rem',
        background: `linear-gradient(95deg, ${primaryColor}, ${primaryDarkColor})`,
        border: 'none',
        borderRadius: '50px',
        boxShadow: `0 8px 25px rgba(${primaryRgb}, 0.3)`,
        transition: 'all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1)',
        transform: 'translateY(0)',
        color: '#fff',
        textDecoration: 'none',
        display: 'inline-block'
    };



    const featureImageContainerStyle = (isHovered) => ({
        borderRadius: '1.25rem',
        overflow: 'hidden',
        boxShadow: isHovered ? '0 1.25rem 3.5rem rgba(0,0,0,0.2)' : '0 0.75rem 2rem rgba(0,0,0,0.1)',
        transition: 'transform 0.4s ease-out, box-shadow 0.4s ease-out',
        transform: isHovered ? 'scale(1.04) translateY(-8px)' : 'scale(1) translateY(0)',
        backgroundColor: '#f0f2f5',
    });

    const featureImageStyle = {
        width: '100%',
        height: '100%',
        minHeight: '400px',
        objectFit: 'cover',
        transition: 'transform 0.6s ease',
    };

    return (
        <div className="contract-staffing-page-wrapper">
            {/* Hero Section */}
            <section
                ref={heroRef}
                className="contract-staffing-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
                style={{
                    backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${contractStaffingData.hero.backgroundImage})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    minHeight: '100vh',
                    padding: '8rem 0'
                }}
            >
                {/* Animated Background Elements */}
                <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
                    {/* Floating Contract Icons */}
                    {[
                        { icon: 'fa-file-contract', top: '15%', left: '10%', delay: 0 },
                        { icon: 'fa-clock', top: '25%', right: '15%', delay: 1 },
                        { icon: 'fa-handshake', bottom: '20%', left: '8%', delay: 2 },
                        { icon: 'fa-users-cog', bottom: '30%', right: '12%', delay: 3 }
                    ].map((item, index) => (
                        <div
                            key={index}
                            className="position-absolute"
                            style={{
                                ...item,
                                width: '60px',
                                height: '60px',
                                background: 'rgba(0, 160, 233, 0.1)',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                backdropFilter: 'blur(10px)',
                                border: '1px solid rgba(0, 160, 233, 0.2)',
                                animation: `float 6s ease-in-out infinite`,
                                animationDelay: `${item.delay}s`
                            }}
                        >
                            <i
                                className={`fas ${item.icon}`}
                                style={{
                                    color: 'rgba(0, 160, 233, 0.8)',
                                    fontSize: '1.5rem'
                                }}
                            />
                        </div>
                    ))}

                    {/* Gradient Orbs */}
                    <div
                        className="position-absolute"
                        style={{
                            width: '300px',
                            height: '300px',
                            background: 'radial-gradient(circle, rgba(0, 160, 233, 0.15) 0%, transparent 70%)',
                            borderRadius: '50%',
                            top: '20%',
                            right: '10%',
                            filter: 'blur(40px)',
                            animation: 'pulse 4s ease-in-out infinite'
                        }}
                    />
                </div>
                <Container className="position-relative z-index-1">
                    <h1 className="display-2 fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow" style={{ textShadow: '3px 3px 6px rgba(0,0,0,0.6)' }}>
                        {contractStaffingData.hero.title}
                    </h1>
                    <p className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow" style={{ maxWidth: '800px', textShadow: '1px 1px 3px rgba(0,0,0,0.4)', fontSize: '1.35rem' }}>
                        {contractStaffingData.hero.subtitle}
                    </p>
                    <Link
                        to="/contact"
                        className="btn btn-lg rounded-pill fw-bold animate__animated animate__zoomIn animate__delay-1s"
                        style={{
                            ...ctaButtonBaseStyle,
                            backgroundColor: 'rgba(255,255,255,0.18)',
                            borderColor: 'rgba(255,255,255,0.6)',
                            backdropFilter: 'blur(8px)',
                            boxShadow: '0 6px 20px rgba(0,0,0,0.25)',
                            padding: '1.2rem 3.5rem',
                            fontSize: '1.25rem'
                        }}
                    >
                        Connect With Our Contract Staffing Experts <i className="fas fa-arrow-right ms-2"></i>
                    </Link>
                </Container>
            </section>

            {/* Feature Sections */}
            <div className="py-5 py-md-6" style={{
                background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)',
                backdropFilter: 'blur(10px)'
            }}>
                <Container>
                    {contractStaffingData.sections.map((section, idx) => (
                        <section
                            key={section.id}
                            ref={el => sectionsRef.current[idx] = el}
                            className="mb-5 mb-md-6 py-3"
                        >
                            {/* Centered Heading Above Content */}
                            <Row className="mb-4 mb-md-5">
                                <Col xs={12}>
                                    <h2 className="display-5 fw-bold text-center" style={{
                                        color: '#ffffff',
                                        lineHeight: '1.2',
                                        marginBottom: '0'
                                    }}>
                                        {section.title}
                                    </h2>
                                </Col>
                            </Row>

                            {/* Content and Image Row */}
                            <Row className={`align-items-center g-4 g-lg-5 ${section.reversed ? 'flex-row-reverse' : ''}`}>
                                <Col lg={6} md={12} className={`${section.reversed ? 'ps-lg-4' : 'pe-lg-4'}`}>
                                    <div className="content-wrapper">
                                        {section.texts.map((text, textIdx) => (
                                            <p key={textIdx} className="mb-3 mb-md-4" style={{
                                                fontSize: '1.1rem',
                                                lineHeight: '1.7',
                                                color: 'rgba(255, 255, 255, 0.9)',
                                                textAlign: 'justify'
                                            }}>
                                                {text}
                                            </p>
                                        ))}
                                        {section.listItems && (
                                            <div className="mt-3 mt-md-4 mb-3 mb-md-4">
                                                {section.listItems.map((item, itemIdx) => (
                                                    <div
                                                        key={itemIdx}
                                                        className="d-flex align-items-start mb-3"
                                                        style={{
                                                            fontSize: '1rem',
                                                            lineHeight: '1.6',
                                                            color: 'rgba(255, 255, 255, 0.85)'
                                                        }}
                                                    >
                                                        <i className="fas fa-check-circle me-3 flex-shrink-0 mt-1" style={{
                                                            fontSize: '1.1rem',
                                                            color: '#00a0e9'
                                                        }}></i>
                                                        <span style={{ textAlign: 'justify' }}>{item}</span>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                        <Link
                                            to={`/services/contract-staffing/${section.id}`}
                                            className="btn btn-outline-light btn-sm mt-2 fw-semibold"
                                            style={{
                                                fontSize: '0.95rem',
                                                borderRadius: '25px',
                                                padding: '8px 20px',
                                                transition: 'all 0.3s ease',
                                                borderColor: '#00a0e9',
                                                color: '#00a0e9'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = '#00a0e9';
                                                e.target.style.color = '#ffffff';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = 'transparent';
                                                e.target.style.color = '#00a0e9';
                                            }}
                                        >
                                            Learn More <i className="fas fa-arrow-right ms-2 small"></i>
                                        </Link>
                                    </div>
                                </Col>
                                <Col lg={6} md={12} className="d-flex justify-content-center">
                                    <div
                                        className="image-container"
                                        style={{
                                            ...featureImageContainerStyle(hoveredImage === section.id),
                                            maxWidth: '100%',
                                            width: '100%'
                                        }}
                                        onMouseEnter={() => setHoveredImage(section.id)}
                                        onMouseLeave={() => setHoveredImage(null)}
                                    >
                                        <Image
                                            src={section.image}
                                            alt={section.alt}
                                            fluid
                                            className="animate__animated animate__fadeInRight animate__delay-0.5s"
                                            style={{
                                                ...featureImageStyle,
                                                transform: hoveredImage === section.id ? 'scale(1.05)' : 'scale(1)',
                                                height: '350px',
                                                objectFit: 'cover'
                                            }}
                                        />
                                    </div>
                                </Col>
                            </Row>
                        </section>
                    ))}
                </Container>
            </div>



            {/* Global CSS */}
            <style>{`
                :root {
                    --bs-primary: ${primaryColor};
                    --bs-primary-dark: ${primaryDarkColor};
                    --bs-primary-rgb: ${primaryRgb};
                }

                h1, h2, h3, h4, h5, h6 {
                    line-height: 1.2;
                }

                p {
                    line-height: 1.75;
                }

                .container {
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                }

                .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

                .content-wrapper {
                    padding: 0;
                }

                .image-container {
                    position: relative;
                    overflow: hidden;
                }

                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 0.6;
                    }
                    33% {
                        transform: translateY(-15px) rotate(120deg);
                        opacity: 1;
                    }
                    66% {
                        transform: translateY(5px) rotate(240deg);
                        opacity: 0.8;
                    }
                }

                @keyframes pulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.15;
                    }
                    50% {
                        transform: scale(1.1);
                        opacity: 0.25;
                    }
                }

                @media (min-width: 768px) {
                    .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
                    .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
                    .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
                    .mb-md-5 { margin-bottom: 4rem !important; }
                    .mb-md-6 { margin-bottom: 6rem !important; }
                    .mb-md-8 { margin-bottom: 8rem !important; }
                }
            `}</style>
        </div>
    );
};

export default ContractStaffingPage;
