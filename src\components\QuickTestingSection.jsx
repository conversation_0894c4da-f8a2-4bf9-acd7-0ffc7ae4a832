import React, { useState } from 'react';

// Helper for hover styles (can be reused across components)
const getHoverStyles = (isHovered, baseStyle, hoverStyle) => {
  return isHovered ? { ...baseStyle, ...hoverStyle } : baseStyle;
};

const QuickTestingSection = () => {
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const [isImageHovered, setIsImageHovered] = useState(false);

  const imageContainerBaseStyle = {
    borderRadius: '1rem', // Softer, larger radius (Bootstrap rounded-4)
    overflow: 'hidden',
    boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.1)', // Softer, more modern shadow
    transition: 'transform 0.35s ease-out, box-shadow 0.35s ease-out',
    transform: 'scale(1)',
  };

  const imageContainerHoverStyle = {
    transform: 'scale(1.03) translateY(-5px)',
    boxShadow: '0 1rem 2rem rgba(0, 86, 179, 0.15)', // A bit more lift and color hint
  };

  const imageStyle = {
    transition: 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)', // For a smoother scale if applied directly
    height: 'auto', // Maintain aspect ratio
    // transform: isImageHovered ? 'scale(1.05)' : 'scale(1)', // Alternative: scale image inside static container
  };

  const ctaButtonBaseStyle = {
    padding: '0.9rem 2.2rem', // Adjusted padding
    fontSize: '1.05rem',
    background: 'linear-gradient(95deg, #0056b3, #007bff)', // Slightly adjusted gradient
    border: 'none',
    boxShadow: '0 5px 15px rgba(0, 86, 179, 0.25)',
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    transform: 'translateY(0)',
  };

  const ctaButtonHoverStyle = {
    background: 'linear-gradient(95deg, #004a99, #0069d9)', // Darken on hover
    boxShadow: '0 8px 20px rgba(0, 86, 179, 0.35)',
    transform: 'translateY(-4px) scale(1.02)', // More pronounced lift
  };

  return (
    <section
      className="py-16 lg:py-24 backdrop-blur-sm"
      id="quick-testing-section"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)'
      }}
    >
      <div className="container-makonis">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div className="lg:pr-8">
            <h6 className="font-bold text-xs uppercase tracking-wider text-makonis-secondary mb-4">
              Flexible & Efficient
            </h6>
            <h2 className="text-4xl lg:text-6xl font-bold mb-6 text-white leading-tight">
              Streamlined Testing Solutions
            </h2>
            <p className="text-xl leading-relaxed text-white/90 mb-6">
              Traditional unit tests meticulously examine every code piece. You provide inputs, assert outputs, but sometimes test cases are limited, and dedicated, lengthy testing cycles aren't feasible.
            </p>
            <p className="text-base leading-relaxed text-white/80 mb-6">
              This conventional method can overly focus on <em className="font-medium not-italic text-makonis-secondary">internal</em> application workings. We offer a smarter way: test only what's necessary. Hire a testing engineer on your terms—even for a single test case. That's our flexibility promise.
            </p>
            <p className="text-base leading-relaxed text-white/80 mb-8">
              Imagine verifying your application's expected behavior without getting bogged down in implementation details. We make that a reality.
            </p>
            <button
              className="btn-makonis-primary inline-flex items-center"
              onMouseEnter={() => setIsButtonHovered(true)}
              onMouseLeave={() => setIsButtonHovered(false)}
            >
              Discuss Your Testing Needs
              <lord-icon
                src="https://cdn.lordicon.com/zmkotitn.json"
                trigger="hover"
                colors="primary:#ffffff"
                style={{ width: '20px', height: '20px', marginLeft: '8px' }}>
              </lord-icon>
            </button>
          </div>
          <div>
            <div
              className="rounded-2xl overflow-hidden shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2"
              onMouseEnter={() => setIsImageHovered(true)}
              onMouseLeave={() => setIsImageHovered(false)}
              style={{
                boxShadow: isImageHovered
                  ? '0 25px 50px rgba(0, 86, 179, 0.15)'
                  : '0 10px 25px rgba(0, 0, 0, 0.1)'
              }}
            >
              <img
                src="https://images.unsplash.com/photo-1516321497487-e288fb19713f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
                alt="Software testing process"
                className="w-full h-auto transition-transform duration-500"
              />
            </div>
            <p className="text-center mt-4 text-white/70 text-sm italic">
              Our testing approach adapts to your unique project requirements.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default QuickTestingSection;