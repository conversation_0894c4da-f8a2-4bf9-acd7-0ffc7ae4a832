import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

// Import local assets
import Picture1 from '../Asserts/Picture1.png';
import Picture2 from '../Asserts/Picture2.png';
import Picture3 from '../Asserts/Picture3.png';
import Picture4 from '../Asserts/Picture4.png';
import Picture5 from '../Asserts/Picture5.png';
import Picture6 from '../Asserts/Picture6.png';
import Picture7 from '../Asserts/Picture7.png';
import Picture8 from '../Asserts/Picture8.png';

const GallerySection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState({});
  const carouselRef = useRef(null);
  const slideRefs = useRef([]);
  const dotsRef = useRef([]);

  // Gallery images with local assets
  const galleryImages = [
    {
      id: 1,
      src: Picture1,
      alt: 'Technology Solutions',
      caption: 'Innovative Technology Solutions',
      description: 'Cutting-edge software development and digital transformation'
    },
    {
      id: 2,
      src: Picture2,
      alt: 'AI & Machine Learning',
      caption: 'AI & Machine Learning',
      description: 'Advanced artificial intelligence and machine learning solutions'
    },
    {
      id: 3,
      src: Picture3,
      alt: 'Cloud Infrastructure',
      caption: 'Cloud Infrastructure',
      description: 'Scalable cloud solutions and infrastructure management'
    },
    {
      id: 4,
      src: Picture4,
      alt: 'Mobile Development',
      caption: 'Mobile Development',
      description: 'Cross-platform mobile applications and responsive design'
    },
    {
      id: 5,
      src: Picture5,
      alt: 'Data Analytics',
      caption: 'Data Analytics',
      description: 'Business intelligence and data-driven insights'
    },
    {
      id: 6,
      src: Picture6,
      alt: 'Cybersecurity',
      caption: 'Cybersecurity Solutions',
      description: 'Enterprise-grade security and compliance solutions'
    },
    {
      id: 7,
      src: Picture7,
      alt: 'IoT Solutions',
      caption: 'IoT Solutions',
      description: 'Internet of Things and connected device ecosystems'
    },
    {
      id: 8,
      src: Picture8,
      alt: 'Digital Transformation',
      caption: 'Digital Transformation',
      description: 'Complete digital transformation and modernization'
    }
  ];

  // Auto-advance carousel functionality
  useEffect(() => {
    if (!isPlaying || isHovered) return;

    const interval = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % galleryImages.length);
    }, 4000); // 4 seconds per slide

    return () => clearInterval(interval);
  }, [isPlaying, isHovered, galleryImages.length]);

  // Handle image loading
  const handleImageLoad = (imageId) => {
    setImageLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  // Navigation functions
  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + galleryImages.length) % galleryImages.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % galleryImages.length);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  // GSAP animations for slide transitions
  useEffect(() => {
    if (slideRefs.current[currentSlide]) {
      gsap.fromTo(slideRefs.current[currentSlide],
        { opacity: 0, scale: 1.1 },
        { opacity: 1, scale: 1, duration: 0.8, ease: "power2.out" }
      );
    }

    // Animate dots
    dotsRef.current.forEach((dot, index) => {
      if (dot) {
        gsap.to(dot, {
          scale: index === currentSlide ? 1.2 : 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  }, [currentSlide]);




  return (
    <section
      className="position-relative overflow-hidden"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #002956 0%, #001a3a 50%, #000f1f 100%)',
        display: 'flex',
        alignItems: 'center',
        padding: '80px 0'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Grid Pattern Overlay - Same as ATSDemo */}
      <div
        className="position-absolute w-100 h-100"
        style={{
          background: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>')`,
          opacity: 0.3,
          pointerEvents: 'none',
          zIndex: 0
        }}
      />

      {/* Floating Geometric Shapes */}
      <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="position-absolute"
            style={{
              width: `${60 + Math.random() * 40}px`,
              height: `${60 + Math.random() * 40}px`,
              background: `linear-gradient(45deg, rgba(0, 160, 233, 0.1), rgba(0, 160, 233, 0.3))`,
              borderRadius: Math.random() > 0.5 ? '50%' : '20%',
              top: `${Math.random() * 80 + 10}%`,
              left: `${Math.random() * 80 + 10}%`,
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 160, 233, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 160, 233, 0.1)',
              animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      <div className="container position-relative" style={{ zIndex: 2 }}>
        {/* Section Header */}
        <div className="text-center mb-5">
          <h2
            className="display-4 fw-bold mb-4"
            style={{
              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
            }}
          >
            Our Portfolio Showcase
          </h2>
          <p
            className="lead mb-4"
            style={{
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '1.2rem',
              maxWidth: '600px',
              margin: '0 auto'
            }}
          >
            Discover our innovative solutions and cutting-edge technology implementations
          </p>
        </div>

        {/* Carousel Container */}
        <div
          ref={carouselRef}
          className="position-relative"
          style={{
            height: '500px',
            background: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(20px)',
            borderRadius: '24px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            boxShadow: '0 20px 60px rgba(0, 43, 89, 0.3), 0 8px 25px rgba(0, 157, 230, 0.2)',
            overflow: 'hidden'
          }}
        >
          {/* Carousel Slides */}
          <div className="position-relative w-100 h-100">
            {galleryImages.map((image, index) => (
              <div
                key={image.id}
                ref={el => slideRefs.current[index] = el}
                className="position-absolute w-100 h-100"
                style={{
                  opacity: index === currentSlide ? 1 : 0,
                  transition: 'opacity 0.8s ease-in-out',
                  zIndex: index === currentSlide ? 2 : 1
                }}
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-100 h-100"
                  style={{
                    objectFit: 'cover',
                    filter: 'brightness(0.8)'
                  }}
                  onLoad={() => handleImageLoad(image.id)}
                />

                {/* Image Overlay with Content */}
                <div
                  className="position-absolute w-100 h-100 d-flex align-items-center"
                  style={{
                    background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.7) 0%, rgba(0, 157, 230, 0.3) 100%)',
                    top: 0,
                    left: 0
                  }}
                >
                  <div className="container">
                    <div className="row align-items-center">
                      <div className="col-lg-6">
                        <div className="text-white">
                          <h3
                            className="display-5 fw-bold mb-3"
                            style={{
                              background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                              WebkitBackgroundClip: 'text',
                              WebkitTextFillColor: 'transparent',
                              backgroundClip: 'text'
                            }}
                          >
                            {image.caption}
                          </h3>
                          <p
                            className="lead mb-4"
                            style={{
                              color: 'rgba(255, 255, 255, 0.9)',
                              fontSize: '1.1rem'
                            }}
                          >
                            {image.description}
                          </p>
                          <div
                            className="d-inline-block px-4 py-2 rounded-pill"
                            style={{
                              background: 'rgba(0, 160, 233, 0.2)',
                              border: '1px solid rgba(0, 160, 233, 0.3)',
                              backdropFilter: 'blur(10px)'
                            }}
                          >
                            <small style={{ color: '#00a0e9', fontWeight: '600' }}>
                              {index + 1} of {galleryImages.length}
                            </small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Loading State */}
                {!imageLoaded[image.id] && (
                  <div
                    className="position-absolute w-100 h-100 d-flex align-items-center justify-content-center"
                    style={{
                      background: 'rgba(0, 41, 86, 0.9)',
                      top: 0,
                      left: 0,
                      zIndex: 3
                    }}
                  >
                    <div className="text-center">
                      <div
                        className="spinner-border mb-3"
                        style={{ color: '#00a0e9' }}
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <p style={{ color: 'rgba(255, 255, 255, 0.8)' }}>Loading image...</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
          {/* Navigation Controls */}
          <div className="position-absolute top-50 start-0 translate-middle-y ms-3" style={{ zIndex: 3 }}>
            <button
              onClick={goToPrevious}
              className="btn d-flex align-items-center justify-content-center"
              style={{
                width: '50px',
                height: '50px',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                color: 'white',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(0, 160, 233, 0.3)';
                e.target.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                e.target.style.transform = 'scale(1)';
              }}
              aria-label="Previous image"
            >
              <i className="fas fa-chevron-left"></i>
            </button>
          </div>

          <div className="position-absolute top-50 end-0 translate-middle-y me-3" style={{ zIndex: 3 }}>
            <button
              onClick={goToNext}
              className="btn d-flex align-items-center justify-content-center"
              style={{
                width: '50px',
                height: '50px',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                color: 'white',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(0, 160, 233, 0.3)';
                e.target.style.transform = 'scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                e.target.style.transform = 'scale(1)';
              }}
              aria-label="Next image"
            >
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>

          {/* Play/Pause Button */}
          <div className="position-absolute top-0 end-0 m-3" style={{ zIndex: 3 }}>
            <button
              onClick={togglePlayPause}
              className="btn d-flex align-items-center justify-content-center"
              style={{
                width: '40px',
                height: '40px',
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '50%',
                color: 'white',
                fontSize: '14px',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(0, 160, 233, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
              aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
            >
              <i className={`fas fa-${isPlaying ? 'pause' : 'play'}`}></i>
            </button>
          </div>
        </div>

        {/* Dot Indicators */}
        <div className="d-flex justify-content-center mt-4">
          {galleryImages.map((_, index) => (
            <button
              key={index}
              ref={el => dotsRef.current[index] = el}
              onClick={() => goToSlide(index)}
              className="btn p-0 mx-2"
              style={{
                width: '12px',
                height: '12px',
                borderRadius: '50%',
                background: index === currentSlide
                  ? 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)'
                  : 'rgba(255, 255, 255, 0.3)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                transition: 'all 0.3s ease',
                boxShadow: index === currentSlide
                  ? '0 0 20px rgba(0, 160, 233, 0.5)'
                  : 'none'
              }}
              onMouseEnter={(e) => {
                if (index !== currentSlide) {
                  e.target.style.background = 'rgba(255, 255, 255, 0.5)';
                }
              }}
              onMouseLeave={(e) => {
                if (index !== currentSlide) {
                  e.target.style.background = 'rgba(255, 255, 255, 0.3)';
                }
              }}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.7;
          }
          33% {
            transform: translateY(-15px) rotate(120deg);
            opacity: 1;
          }
          66% {
            transform: translateY(5px) rotate(240deg);
            opacity: 0.8;
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        /* Carousel specific animations */
        .carousel-slide-enter {
          opacity: 0;
          transform: translateX(100%);
        }

        .carousel-slide-enter-active {
          opacity: 1;
          transform: translateX(0);
          transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
        }

        .carousel-slide-exit {
          opacity: 1;
          transform: translateX(0);
        }

        .carousel-slide-exit-active {
          opacity: 0;
          transform: translateX(-100%);
          transition: opacity 0.8s ease-in-out, transform 0.8s ease-in-out;
        }
      `}</style>
    </section>
  );
};

export default GallerySection;