import React, { useState, useEffect } from 'react';

const GallerySection = () => {
  const [index, setIndex] = useState(0);
  const [hoveredImage, setHoveredImage] = useState(null); // To manage hover state for individual images

  const handleSelect = (selectedIndex) => {
    setIndex(selectedIndex);
  };

  // Improved data structure for gallery images
  const gallerySlides = [
    [
      { id: 'img1', src: 'https://images.unsplash.com/photo-1534087334496-PF895bf8c999?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Modern Architecture', caption: 'Sleek Design' },
      { id: 'img2', src: 'https://images.unsplash.com/photo-1554995207-c18c203602cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Interior Space', caption: 'Comfort & Style' },
      { id: 'img3', src: 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Luxury Home Exterior', caption: 'Grand Entrance' }
    ],
    [
      { id: 'img4', src: 'https://images.unsplash.com/photo-1580587771525-78b9dba3b914?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Contemporary Living Room', caption: 'Open Concept' },
      { id: 'img5', src: 'https://images.unsplash.com/photo-1570129477492-45c003edd2e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Suburban House', caption: 'Family Home' },
      { id: 'img6', src: 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Poolside Villa', caption: 'Relaxation Zone' }
    ],
    [
      { id: 'img7', src: 'https://images.unsplash.com/photo-1480074568708-e7b720bb3f09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'House with Garden', caption: 'Green Living' },
      { id: 'img8', src: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Minimalist Bedroom', caption: 'Serene Sleep' },
      { id: 'img9', src: 'https://images.unsplash.com/photo-1494526585095-c41746248156?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80', alt: 'Modern Kitchen', caption: 'Culinary Space' }
    ]
  ];
  // Using Unsplash for more realistic placeholders. Replace with your actual images.

  // Auto-advance carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prevIndex) => (prevIndex + 1) % gallerySlides.length);
    }, 5000); // Keep auto-advance interval reasonable

    return () => clearInterval(interval);
  }, [gallerySlides.length]);

  const imageContainerStyle = (imgId) => {
    const isHovered = hoveredImage === imgId;
    return {
      borderRadius: '0.75rem', // Bootstrap's rounded-3
      overflow: 'hidden',
      boxShadow: isHovered ? '0 10px 25px rgba(0, 0, 0, 0.15)' : '0 4px 12px rgba(0, 0, 0, 0.08)',
      height: '100%',
      minHeight: '260px', // Ensure a minimum height for consistency
      transition: 'transform 0.35s ease-out, box-shadow 0.35s ease-out',
      transform: isHovered ? 'scale(1.03) translateY(-5px)' : 'scale(1) translateY(0)',
      position: 'relative', // For caption overlay
      cursor: 'pointer', // Indicate clickability (for future lightbox)
    };
  };

  const imageStyle = { // Applied directly to Bootstrap <Image>
    width: '100%',
    height: '100%',
    objectFit: 'cover', // Ensures image covers the area, might crop
    transition: 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)', // Smoother scale
    // Scale is handled by parent container's transform for a combined effect
  };

  const captionOverlayStyle = (imgId) => {
    const isHovered = hoveredImage === imgId;
    return {
        position: 'absolute',
        bottom: 0,
        left: 0,
        width: '100%',
        background: 'linear-gradient(to top, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0) 100%)',
        color: 'white',
        padding: '1rem 0.75rem 0.5rem 0.75rem',
        textAlign: 'center',
        opacity: isHovered ? 1 : 0,
        transform: isHovered ? 'translateY(0)' : 'translateY(10px)',
        transition: 'opacity 0.3s ease, transform 0.3s ease',
        fontSize: '0.9rem',
    };
  };


  return (
    <section
      className="py-16 lg:py-24 backdrop-blur-sm"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)'
      }}
      id="gallery-section"
    >
      <div className="container-makonis">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-4xl lg:text-6xl font-bold mb-4 text-white">Our Work & Inspirations</h2>
          <p className="text-xl text-white/90 leading-relaxed mx-auto mb-6 max-w-3xl">
            A glimpse into our projects, designs, and the quality we deliver.
          </p>
          <div className="w-20 h-1 mx-auto rounded-sm" style={{ background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)' }}></div>
        </div>

        {/* Enhanced Gallery Grid */}
        <div className="card-makonis-glass p-6 lg:p-8 rounded-3xl shadow-2xl">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {gallerySlides.flat().map((image) => (
              <div
                key={image.id}
                className="group relative overflow-hidden rounded-xl cursor-pointer transition-all duration-500 hover:-translate-y-2 hover:scale-105"
                style={{
                  height: '280px',
                  boxShadow: hoveredImage === image.id
                    ? '0 25px 50px rgba(0, 0, 0, 0.3)'
                    : '0 10px 25px rgba(0, 0, 0, 0.15)'
                }}
                onMouseEnter={() => setHoveredImage(image.id)}
                onMouseLeave={() => setHoveredImage(null)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ')
                    alert(`Open ${image.caption} in lightbox!`);
                }}
              >
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />

                {/* Enhanced Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/75 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-4">
                    <h3 className="text-white font-semibold text-lg mb-1 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
                      {image.caption}
                    </h3>
                    <div className="w-12 h-0.5 bg-makonis-secondary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                  </div>
                </div>

                {/* Shine Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out"></div>
              </div>
            ))}
          </div>
        </div>
        {/* Enhanced Call to Action */}
        <div className="text-center mt-8">
          <p className="text-white/70 text-sm italic mb-4">
            Tip: Click on an image to view larger (lightbox functionality coming soon!).
          </p>
          <div className="btn-makonis-ghost inline-flex items-center cursor-pointer hover:-translate-y-1 hover:shadow-glow">
            <span className="text-white font-semibold mr-3">
              View Our Complete Portfolio
            </span>
            <i className="fas fa-arrow-right text-makonis-secondary"></i>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GallerySection;